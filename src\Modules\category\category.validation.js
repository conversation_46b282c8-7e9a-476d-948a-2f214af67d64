// import joi from "joi";
// import { generalFeild } from "../../services/geniralFeild.validation.js";

// export const  getCategories= joi.object({
//   // myid:generalFeild.id
//   myid:joi.string()
// }).required();

// export const categoryValidation = joi.object({
//     name: joi.string().min(5).max(20),
//     // categoryId:generalFeild.id.required(),
//     file: generalFeild.file.required(),
//   })
//   .required();

// export const updateCategory = joi
//   .object({
//    categoryId:generalFeild.id,
//     // categoryId: joi.string().min(24).max(24).required(),
//     name: joi.string().min(5).max(20), //not required .required(),
//     file: generalFeild.file,

//     // its optional to add file or not
//   })
//   .required();
