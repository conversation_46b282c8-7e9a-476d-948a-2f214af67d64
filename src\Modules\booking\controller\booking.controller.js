import { asyncHand<PERSON> } from "../../../Services/ErrorHandler.services.js";
import Booking from "../../../../database/Models/booking.models.js";
import { sendEmail } from "../../../Services/SendEmail.services.js";
 

 
// ---------- utils ----------
const toMinutes = (timeStr) => {
  const [h, m] = String(timeStr).split(":").map(Number);
  return h * 60 + m;
};

// Normalize "YYYY-MM-DD" or "YYYY/MM/DD" -> Date at local 00:00:00.000
const getDayBounds = (dateStr) => {
  const normalized = dateStr.replaceAll("/", "-"); // allow both separators
  const dayStart = new Date(normalized);
  if (Number.isNaN(dayStart.getTime())) throw new Error("Invalid dateOfRent");

  dayStart.setHours(0, 0, 0, 0);

  const dayEnd = new Date(dayStart);
  dayEnd.setHours(23, 59, 59, 999);
  return { dayStart, dayEnd };
};

const validateNotPastDate = (dayStart) => {
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  if (dayStart < todayStart) {
    const e = new Error("Date of rent must be today or in the future");
    e.statusCode = 400;
    throw e;
  }
};

const validateTimeOrder = (startTime, endTime) => {
  const s = toMinutes(startTime);
  const e = toMinutes(endTime);
  if (e <= s) {
    const err = new Error("End time must be after start time");
    err.statusCode = 400;
    throw err;
  }
};

const validateNotPastTimeToday = (dayStart, startTime) => {
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const isToday = dayStart.getTime() === todayStart.getTime();

  if (isToday) {
    const currentMinutes = now.getHours() * 60 + now.getMinutes();
    const startMinutes = toMinutes(startTime);
    if (startMinutes < currentMinutes) {
      const e = new Error("Start time must be in the future (cannot book past time today)");
      e.statusCode = 400;
      throw e;
    }
  }
};

const calculatePrice = (durationHours, pricePerHour = 70) => {
  return Math.round(Number(durationHours) * pricePerHour * 100) / 100;
};

// ---------- controller ----------
export const createBooking = asyncHandler(async (req, res, next) => {
  // If you have auth middleware, you might have: const userId = req.user?.id;
     const userID= req.user._id;
  const {
    userId ,
    renterName,
    renterPhone,
    renterEmail,
    dateOfRent,
    startTime,
    endTime,
    durationHours,
    anyComment,
    fieldId, // optional
  } = req.body;

  // 1) Day bounds
  const { dayStart, dayEnd } = getDayBounds(dateOfRent);

  // 2) Date not in past
  validateNotPastDate(dayStart);

  // 3) End > Start
  validateTimeOrder(startTime, endTime);

  // 4) If booking today, start must be >= now
  validateNotPastTimeToday(dayStart, startTime);

  // 5) Check overlaps (same field or global if single field)
  const match = {
    dateOfRent: { $gte: dayStart, $lte: dayEnd },
    status: { $ne: "cancelled" },
  };
  if (fieldId) match.fieldId = fieldId;

  const sameDay = await Booking.find(match).lean();

  const newStart = toMinutes(startTime);
  const newEnd   = toMinutes(endTime);

  const conflict = sameDay.find((b) => {
    const bStart = toMinutes(b.startTime);
    const bEnd   = toMinutes(b.endTime);
    // overlap if NOT (newEnd <= bStart || newStart >= bEnd)
    return !(newEnd <= bStart || newStart >= bEnd);
  });

  if (conflict) {
    return res.status(409).json({
      message: "Time slot not available",
      conflict: {
        dateOfRent: conflict.dateOfRent,
        startTime:  conflict.startTime,
        endTime:    conflict.endTime,
      },
    });
  }

  // 6) Pricing
  const pricePerHour = 70;
  const totalPrice = calculatePrice(durationHours, pricePerHour);

  // 7) Create
  const created = await Booking.create({
    
    userId:userID,
    renterName,
    renterPhone,
    renterEmail,
    dateOfRent: dayStart, // store normalized date
    startTime,
    endTime,
    durationHours: Number(durationHours),
    pricePerHour,
    totalPrice,
    anyComment,
    status: "pending",
    ...(fieldId ? { fieldId } : {}),
    // createdBy: userId, // if you use auth
  });


 
 





  return res.status(201).json({
    success: true,
    message: "Booking created",
    data: created,
  });
});

 