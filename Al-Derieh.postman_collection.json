{"info": {"_postman_id": "5edbdf49-fc44-4e94-99af-f01fa7964c2f", "name": "rent-soccer", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "43052430", "_collection_link": "https://ahmad6-8020.postman.co/workspace/fd30b6e1-67e5-4918-8fb3-417afc41ee28/collection/43052430-5edbdf49-fc44-4e94-99af-f01fa7964c2f?action=share&source=collection_link&creator=43052430"}, "item": [{"name": "auth", "item": [{"name": "signup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userName\": \"ahmadQutob\",\r\n    \"password\": \"ahmad qutob\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"phone\": \"0597222749\",\r\n    \"role\": \"admin\",\r\n    \"gender\": \"male\" \r\n     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/signup", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "signup"]}}, "response": []}, {"name": "confirmEmail", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/auth/ss", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "ss"]}}, "response": []}, {"name": "checkConfirmEmail", "request": {"method": "GET", "header": []}, "response": []}, {"name": "signin", "request": {"method": "GET", "header": []}, "response": []}, {"name": "sendCode", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\":\"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/sendcode/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "sendcode", ""]}}, "response": []}, {"name": "changePassword", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"oldPassword\":\"$2b$08$eP6eu3SuFdIezau7ZUhBbuFco4rRXQGNOpivJKqnsQOOL98S.oW3G\",\r\n    \"newPassword\":\"ahmad123456\",\r\n    \"CnewPassword\":\"ahmad123456\" \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/changePassword", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "changePassword"]}}, "response": []}, {"name": "checkForgotPassword/:email", "request": {"method": "GET", "header": []}, "response": []}, {"name": "forgotPassword", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:3000/auth/forgotPassword", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "forgotPassword"]}}, "response": []}]}, {"name": "product", "item": []}]}